# ArcGIS Demo

基于 Vue 3 + TypeScript + ArcGIS API for JavaScript 的完整地图应用演示。

## 🎯 项目介绍

### 🗺️ ArcGIS Demo
基于 Vue 3 + TypeScript + ArcGIS API for JavaScript 的完整地图应用演示。

**功能特性:**
- 🗺️ **交互式地图**: 基于 ArcGIS API for JavaScript 4.24.7
- 🎨 **样式配置**: 实时调整地图图形的填充颜色、边框样式、半径等
- 🎯 **几何操作**: 使用 geometryEngine 创建缓冲区
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎲 **随机样式**: 一键生成随机样式效果

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **地图引擎**: ArcGIS API for JavaScript 4.24.7
- **UI 组件**: Element Plus
- **构建工具**: Vite
- **包管理**: pnpm

## 项目结构

```
src/
├── App.vue                 # 主应用组件
├── main.ts                # 应用入口
├── style.css              # 全局样式
└── components/
    ├── StyleForm.vue      # 样式配置表单
    └── mapComponents.vue  # ArcGIS 地图组件
```

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动开发服务器

```bash
pnpm dev
```

### 3. 构建生产版本

```bash
pnpm build
```

### 4. 预览生产版本

```bash
pnpm preview
```

## 使用说明

### 🗺️ ArcGIS Demo
1. **地图显示**: 应用启动后会显示一个以北京为中心的地图
2. **样式配置**: 在左侧面板中调整各种样式参数
3. **实时更新**: 样式修改会实时反映在地图上的圆形区域
4. **随机样式**: 点击"随机样式"按钮快速生成新的样式效果
5. **重置功能**: 点击"重置"按钮恢复默认样式



## 主要组件说明

### App.vue
- 主应用布局
- 管理样式数据状态
- 协调组件之间的数据传递

### StyleForm.vue
- 样式配置表单
- 支持颜色选择、数值输入、下拉选择等
- 实时发送样式更新事件

### mapComponents.vue
- ArcGIS 地图渲染
- 几何图形创建和样式应用
- 地图视图自动调整

## 开发特性

- **TypeScript 支持**: 完整的类型定义
- **组件化设计**: 模块化的组件结构
- **响应式数据**: Vue 3 Composition API
- **样式隔离**: Scoped CSS
- **热重载**: Vite 开发服务器

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 许可证

MIT License
