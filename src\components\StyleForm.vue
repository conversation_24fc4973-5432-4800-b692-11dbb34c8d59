<template>
  <div class="style-form-container">
    <el-form :model="formData" label-width="120px" class="style-form">
      <el-form-item label="填充颜色">
        <el-color-picker
          v-model="formData.fillColor"
          show-alpha
          @change="handleFormChange"
        />
      </el-form-item>

      <el-form-item label="边框颜色">
        <el-color-picker
          v-model="formData.borderColor"
          show-alpha
          @change="handleFormChange"
        />
      </el-form-item>

      <el-form-item label="边框宽度">
        <el-input-number
          v-model="formData.borderWidth"
          :min="0"
          :max="10"
          :step="1"
          @change="handleFormChange"
        />
      </el-form-item>

      <el-form-item label="线条类型">
        <el-select
          v-model="formData.lineStyle"
          placeholder="请选择线条类型"
          @change="handleFormChange"
        >
          <el-option v-for="item in lineStyleOptions" :key="item.value" :label="item.label" :value="item.value">
            <div class="line-style-option">
              <span>{{ item.label }}</span>
              <div class="line-style-preview" :style="getLineStylePreview(item.value)"></div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="半径(米)">
        <el-input-number
          v-model="formData.radius"
          :min="1000"
          :max="500000"
          :step="1000"
          @change="handleFormChange"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="resetForm" type="default">重置</el-button>
        <el-button @click="applyRandomStyle" type="primary">随机样式</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'

// 定义props
const props = defineProps<{
  initialData?: {
    fillColor: string
    borderColor: string
    borderWidth: number
    lineStyle: string
    radius: number
  }
}>()

// 定义emits
const emit = defineEmits<{
  updateStyle: [data: any]
}>()

// 表单数据
const formData = reactive({
  fillColor: props.initialData?.fillColor || '#409EFF',
  borderColor: props.initialData?.borderColor || '#000000',
  borderWidth: props.initialData?.borderWidth || 2,
  lineStyle: props.initialData?.lineStyle || 'solid',
  radius: props.initialData?.radius || 50000
})

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  }
}, { deep: true })

// 线条类型选项
const lineStyleOptions = [
  { value: 'solid', label: '实线' },
  { value: 'dash', label: '虚线' },
  { value: 'dot', label: '点线' },
  { value: 'dash-dot', label: '虚点线' },
  { value: 'long-dash', label: '长虚线' },
  { value: 'long-dash-dot', label: '长虚点线' },
  { value: 'long-dash-dot-dot', label: '长虚双点线' },
  { value: 'short-dash', label: '短虚线' },
  { value: 'short-dash-dot', label: '短虚点线' },
  { value: 'short-dash-dot-dot', label: '短虚双点线' },
  { value: 'short-dot', label: '短点线' },
]

// 根据线条类型获取预览样式
const getLineStylePreview = (style: string) => {
  const styleMap: Record<string, string> = {
    'solid': 'border-top: 2px solid #333',
    'dash': 'border-top: 2px dashed #333',
    'dot': 'border-top: 2px dotted #333',
    'dash-dot': 'border-image: linear-gradient(to right, #333 50%, transparent 50%) 1; border-width: 2px 0 0 0',
    'long-dash': 'border-top: 2px dashed #333; border-image: linear-gradient(to right, #333 70%, transparent 30%) 1 0 round',
    'long-dash-dot': 'border-image: linear-gradient(to right, #333 60%, transparent 20%) 1; border-width: 2px 0 0 0',
    'long-dash-dot-dot': 'border-image: linear-gradient(to right, #333 50%, transparent 10%, #333 10%, transparent 30%) 1; border-width: 2px 0 0 0',
    'short-dash': 'border-top: 2px dashed #333; border-image: linear-gradient(to right, #333 30%, transparent 70%) 1 0 round',
    'short-dash-dot': 'border-image: linear-gradient(to right, #333 30%, transparent 10%, #333 10%, transparent 50%) 1; border-width: 2px 0 0 0',
    'short-dash-dot-dot': 'border-image: linear-gradient(to right, #333 20%, transparent 10%, #333 10%, transparent 10%, #333 10%, transparent 50%) 1; border-width: 2px 0 0 0',
    'short-dot': 'border-top: 1px dotted #333; border-image: linear-gradient(to right, #333 10%, transparent 90%) 1 0 round',
  }

  return styleMap[style] || styleMap['solid']
}

// 处理表单变化
const handleFormChange = () => {
  emit('updateStyle', { ...formData })
}

// 重置表单
const resetForm = () => {
  formData.fillColor = '#409EFF'
  formData.borderColor = '#000000'
  formData.borderWidth = 2
  formData.lineStyle = 'solid'
  formData.radius = 50000
  handleFormChange()
}

// 随机样式
const applyRandomStyle = () => {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
  const lineStyles = lineStyleOptions.map(option => option.value)

  formData.fillColor = colors[Math.floor(Math.random() * colors.length)]
  formData.borderColor = colors[Math.floor(Math.random() * colors.length)]
  formData.borderWidth = Math.floor(Math.random() * 5) + 1
  formData.lineStyle = lineStyles[Math.floor(Math.random() * lineStyles.length)]
  formData.radius = Math.floor(Math.random() * 200000) + 10000

  handleFormChange()
}
</script>

<style scoped>
.style-form-container {
  width: 100%;
}

.style-form {
  width: 100%;
}

.line-style-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.line-style-preview {
  width: 50px;
  height: 2px;
  margin-left: 10px;
  flex-shrink: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-button) {
  margin-right: 10px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-color-picker) {
  width: 100%;
}
</style>