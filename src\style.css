* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #213547;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* ArcGIS 地图样式 */
.esri-view {
  border-radius: 8px;
  overflow: hidden;
}

.esri-ui-corner .esri-component {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Element Plus 组件样式调整 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-input-number {
  width: 100%;
}

.el-select {
  width: 100%;
}

.el-color-picker {
  width: 100%;
}
