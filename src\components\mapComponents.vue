<template>
  <div class="map-box">
    <div id="mapDom"></div>
  </div>
</template>

<script setup lang="ts">
import { watch, onMounted, shallowRef } from "vue";
import Map from "@arcgis/core/Map"; // 地图核心类，用于创建地图实例
import MapView from "@arcgis/core/views/MapView"; // 地图视图，用于在DOM容器中显示地图
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer"; // 图形图层，用于在地图上添加和管理图形
import Graphic from "@arcgis/core/Graphic"; // 图形类，用于创建点、线、面等几何图形
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol"; // 填充符号，定义面的填充颜色、边框等
import Point from "@arcgis/core/geometry/Point"; // 点几何体，表示地图上的一个点位置

// 新增导入
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine";

const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const myMap = shallowRef();
const graphicsLayer = shallowRef();

// 监听props.data的变化，更新图形样式
watch(
  () => props.data,
  (newVal) => {
    console.log(newVal, "newVal");
    if (myMap.value && graphicsLayer.value) {
      updateGraphics(newVal);
    }
  },
  { deep: true }
);

// 更新图形样式
const updateGraphics = (styleData: any) => {
  // // 清除图层上的所有图形
  graphicsLayer.value.removeAll();

  console.log(styleData, "styleData");
  // 首先创建一个圆[116.4, 39.9]
  const cirCenter = new Point({
    longitude: 116.4,
    latitude: 39.9,
    spatialReference: {
      wkid: 4326,
    },
  });
  const bufferDistabnce = styleData.radius || 50000;
  const bufferGeometry = geometryEngine.geodesicBuffer(
    cirCenter,
    bufferDistabnce,
    "meters"
  );
  const fillSymbol = new SimpleFillSymbol({
    color: styleData.fillColor || "#409EFF",
    outline: {
      color: styleData.borderColor || "#000000",
      width: styleData.borderWidth || 0,
      style: styleData.lineStyle || "solid",
    },
  });
  const graphic = new Graphic({
    geometry: bufferGeometry as any,
    symbol: fillSymbol,
  });

  // 把圆添加到图层中
  graphicsLayer.value.add(graphic);

  // 调整地图视图以适应圆形
  adjustMapView(bufferGeometry);
};
// 添加新函数：调整地图视图以适应几何体
const adjustMapView = (geometry: any) => {
  if (!myMap.value) return;
  // 计算合适的缩放级别
  // 方法1：使用几何体的范围来设置地图视图
  // myMap.value.goTo({
  //   target: geometry.extent.expand(1.2) // 扩大20%以便更好地显示
  // });
  // 使用更智能的方式调整视图
  myMap.value
    .goTo({
      target: geometry,
      // 不强制设置特定的缩放级别，让地图自动计算合适的缩放级别
      options: {
        animate: true,
        duration: 500,
        easing: "ease-in-out",
      },
    })
    .catch((err) => {
      console.error("调整视图时出错:", err);
    });
};
onMounted(() => {
  // 创建地图
  const map = new Map({
    basemap: "osm",
  });

  // 创建地图视图
  const mapView = new MapView({
    map,
    container: "mapDom",
    center: [116.4, 39.9],
    zoom: 15,
  });
  myMap.value = mapView;

  // 创建图层
  const layer = new GraphicsLayer({
    id: "circle-layer",
    title: "圆形区域图层",
  });
  graphicsLayer.value = layer;
  map.add(layer);

  // 地图加载完成后初始化图形
  mapView.when(() => {
    if (props.data) {
      updateGraphics(props.data);
    }
  });
});
</script>

<style scoped>
.map-box {
  width: 100%;
  height: 100%;
  min-height: 400px;
  background-color: #f0f0f0;
}

#mapDom {
  width: 100%;
  height: 100%;
}
</style>
