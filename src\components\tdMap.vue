<template>
  <div class="td-container" id="tdMap"></div>
</template>

<script setup lang="ts">
import { onMounted, shallowRef } from "vue";
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import BaseMap from "@arcgis/core/Basemap";
import WebTileLayer from "@arcgis/core/layers/WebTileLayer";

// 天地图密钥
const tdToken = "d52454825ecd067fadfb6677494fd387";

const mapView = shallowRef();
const init = () => {
  // // 自定义天地图底图
  // const basemap = new BaseMap({
  //   id: "tdBottomMap",
  //   title: "天地图底图",
  //   baseLayers: [
  //     new WebTileLayer({
  //       urlTemplate:
  //         "http://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=" +
  //         tdToken,
  //       title: "天地图影像图层",
  //       id: "td-img",
  //     }),
  //   ],
  // });

  // 天地图底图
  const tdBottomLayer = new WebTileLayer({
    title: "天地图底图",
    id: "tdBottomlayer",
    urlTemplate: `http://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=${tdToken}`,
  });

  // 天地图标记图层
  const td

  const map = new Map({
    layers: [tdBottomLayer],
  });

  const view = new MapView({
    container: "tdMap",
    map: map,
    center: [116.4, 39.9],
    zoom: 8,
  });
  mapView.value = view;
};

onMounted(() => {
  init();
});
</script>

<style scoped>
.td-container {
  width: 100%;
  height: 100%;
}
</style>
